import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/AppSidebar";
import { TopNavbar } from "@/components/TopNavbar";
import { AuthProvider } from "@/contexts/AuthContext";
import { ExchangeProvider } from "@/contexts/ExchangeContext";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { ProtectedRoute } from "@/components/ProtectedRoute";

// Pages
import Landing from "@/pages/Landing";
import Dashboard from "@/pages/Dashboard";
import TradingBots from "@/pages/TradingBots";
import Strategies from "@/pages/Strategies";
import Portfolio from "@/pages/Portfolio";
import TradingHistory from "@/pages/TradingHistory";
import Analytics from "@/pages/Analytics";
import Backtesting from "@/pages/Backtesting";
import RiskManagement from "@/pages/RiskManagement";
import AIAssistant from "@/pages/AIAssistant";
import Account from "@/pages/Account";
import Settings from "@/pages/Settings";
import FluxTraderDashboard from "@/pages/FluxTraderDashboard";
import FluxTraderTest from "@/pages/FluxTraderTest";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider>
      <AuthProvider>
        <ExchangeProvider>
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
        <Routes>
          {/* Landing Page Route */}
          <Route path="/" element={<Landing />} />

          {/* Dashboard Routes */}
          <Route path="/dashboard/*" element={
            <ProtectedRoute>
              <div className="h-screen bg-background glass-page overflow-hidden relative">
              {/* Animated background gradients */}
              <div className="absolute inset-0 bg-gradient-to-br from-primary/8 via-accent-teal/5 to-accent-purple/8 animate-gradient-shift"></div>
              <div className="absolute inset-0 bg-gradient-to-tr from-success/5 via-transparent to-warning/5 animate-pulse opacity-60"></div>
              <div className="relative z-10 h-full">
                <SidebarProvider defaultOpen={true}>
                  <div className="flex h-screen w-full sidebar-layout">
                    <AppSidebar />
                    <div className="flex-1 flex flex-col min-w-0 main-content-area bg-gradient-to-br from-chart-3/8 via-background/20 to-chart-4/8 relative">
                      {/* Main content background overlay - Enhanced brightness */}
                      <div className="absolute inset-0 bg-gradient-to-b from-accent-purple/8 via-primary/6 to-accent-teal/8 opacity-60 pointer-events-none"></div>
                      {/* Additional brightness layer */}
                      <div className="absolute inset-0 bg-gradient-to-br from-card/15 via-transparent to-card/10 pointer-events-none"></div>
                      <div className="relative z-10 flex flex-col h-full">
                        <TopNavbar />
                        <main className="flex-1 p-6 overflow-y-auto overflow-x-hidden main-content-enhanced">
                          <div className="max-w-[1600px] mx-auto">
                            <Routes>
                              <Route path="/" element={<Dashboard />} />
                              <Route path="/bots" element={<TradingBots />} />
                              <Route path="/strategies" element={<Strategies />} />
                              <Route path="/portfolio" element={<Portfolio />} />
                              <Route path="/history" element={<TradingHistory />} />
                              <Route path="/analytics" element={<Analytics />} />
                              <Route path="/backtesting" element={<Backtesting />} />
                              <Route path="/risk" element={<RiskManagement />} />
                              <Route path="/assistant" element={<AIAssistant />} />
                              <Route path="/account" element={<Account />} />
                              <Route path="/settings" element={<Settings />} />
                              <Route path="/fluxtrader/:agentId" element={<FluxTraderDashboard />} />
                              <Route path="/dashboard/fluxtrader/:agentId" element={<FluxTraderDashboard />} />
                              <Route path="/test/fluxtrader/:agentId" element={<FluxTraderTest />} />
                            </Routes>
                          </div>
                        </main>
                      </div>
                    </div>
                  </div>
                </SidebarProvider>
              </div>
            </div>
            </ProtectedRoute>
          } />

          {/* Redirect any unknown routes to landing */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
        </BrowserRouter>
        </TooltipProvider>
        </ExchangeProvider>
      </AuthProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;